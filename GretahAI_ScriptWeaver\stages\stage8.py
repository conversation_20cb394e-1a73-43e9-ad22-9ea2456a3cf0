"""
Stage 8: Script Consolidation and Optimization

This module handles Phase 8 of the GretahAI ScriptWeaver application workflow.
Stage 8 is responsible for taking the combined script from Phase 7 and optimizing it
using Google AI to create a well-structured, cohesive PyTest module following best practices.

Key Features:
- Combined script processing and optimization workflow
- Google AI API integration for script optimization via optimize_script_with_ai in core/ai.py
- Chunked optimization approach for large scripts
- Detailed optimization metrics and comparison
- Proper handling of optimization results (success/failure scenarios)
- Workflow transitions (Stage 8 → Stage 3 for new test case selection)
- Download functionality for optimized scripts

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Proper workflow transitions back to Stage 3

Functions:
    stage8_optimize_script(state): Main Stage 8 function for script optimization
"""

import os
import logging
import streamlit as st
from datetime import datetime
import time
import re

# Import AI functions for script optimization
from core.ai import optimize_script_with_ai

# Import helper function from stage7 for creating combined scripts
from stages.stage7 import create_combined_script

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage8")


def stage8_optimize_script(state):
    """
    Phase 8: Script Consolidation and Optimization.

    This stage takes the combined script from Phase 7 and optimizes it using Google AI
    to create a well-structured, cohesive PyTest module following best practices.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Phase 8: Script Optimization</h2>", unsafe_allow_html=True)

    # Check if prerequisites are met
    if (not hasattr(state, 'combined_script_content') or not state.combined_script_content or
        not hasattr(state, 'combined_script_path') or not state.combined_script_path):
        logger.info("Stage 8: Missing combined script content or path in state, attempting to load or create it")

        # Check if we have a combined_script_path but no content
        if hasattr(state, 'combined_script_path') and state.combined_script_path:
            logger.info(f"Stage 8: Found combined_script_path in state: {state.combined_script_path}")
            try:
                with open(state.combined_script_path, 'r') as f:
                    state.combined_script_content = f.read()
                logger.info(f"Stage 8: Successfully loaded combined script from existing path: {state.combined_script_path} ({len(state.combined_script_content)} characters)")
                st.success(f"✓ Loaded combined script from: {os.path.basename(state.combined_script_path)}")
            except Exception as e:
                error_msg = f"Error reading combined script file from path: {str(e)}"
                logger.error(f"Stage 8: {error_msg}")
                st.error(error_msg)
                # Continue to try creating a new combined script

        # Try to create a new combined script if needed
        if (not hasattr(state, 'combined_script_content') or not state.combined_script_content or
            not hasattr(state, 'combined_script_path') or not state.combined_script_path):
            # Try to load the combined script from the file if available
            if hasattr(state, 'previous_scripts') and state.previous_scripts:
                logger.info(f"Stage 8: Found {len(state.previous_scripts)} previous scripts, creating combined script")
                # Create a combined script file for the entire test case
                combined_script_path = create_combined_script(state)
                if combined_script_path:
                    try:
                        # The create_combined_script function should have already set these in the state,
                        # but let's double-check to be sure
                        if not hasattr(state, 'combined_script_path') or not state.combined_script_path:
                            state.combined_script_path = combined_script_path
                            logger.info(f"Stage 8: Set combined_script_path in state to {combined_script_path}")

                        if not hasattr(state, 'combined_script_content') or not state.combined_script_content:
                            with open(combined_script_path, 'r') as f:
                                state.combined_script_content = f.read()
                            logger.info(f"Stage 8: Set combined_script_content in state ({len(state.combined_script_content)} characters)")

                        logger.info(f"Stage 8: Successfully loaded combined script from: {combined_script_path}")
                        st.success(f"✓ Loaded combined script from: {os.path.basename(combined_script_path)}")
                    except Exception as e:
                        error_msg = f"Error reading combined script file: {str(e)}"
                        logger.error(f"Stage 8: {error_msg}")
                        st.error(error_msg)
                        return
                else:
                    logger.warning("Phase 8: Failed to create combined script, redirecting user to Phase 7")
                    st.warning("⚠️ Please complete Phase 7 first to generate a combined script")
                    return
            else:
                logger.warning("Phase 8: No previous scripts found, redirecting user to Phase 7")
                st.warning("⚠️ Please complete Phase 7 first to generate a combined script")
                return

    # Display the combined script content in an expander
    with st.expander("View Combined Script", expanded=False):
        st.code(state.combined_script_content, language="python")

    # Check if optimization is already complete
    if state.optimization_complete and state.optimized_script_path:
        st.success(f"✓ Script optimization complete: {os.path.basename(state.optimized_script_path)}")
        logger.info(f"Stage 8: Displaying completed optimization results for {state.optimized_script_path}")

        # Show optimization summary
        try:
            with open(state.optimized_script_path, 'r') as f:
                optimized_script_content = f.read()

            # Calculate statistics for the optimized script
            original_script = state.combined_script_content
            original_lines = original_script.count('\n') + 1
            original_chars = len(original_script)

            optimized_lines = optimized_script_content.count('\n') + 1
            optimized_chars = len(optimized_script_content)

            lines_diff = optimized_lines - original_lines
            chars_diff = optimized_chars - original_chars

            lines_percent = (lines_diff / original_lines) * 100 if original_lines > 0 else 0
            chars_percent = (chars_diff / original_chars) * 100 if original_chars > 0 else 0

            # Display optimization metrics
            st.markdown("#### Optimization Results")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Original Lines", f"{original_lines:,}",
                          delta=f"{lines_diff:+,} ({lines_percent:.1f}%)",
                          delta_color="inverse")

            with col2:
                st.metric("Optimized Lines", f"{optimized_lines:,}")

            with col3:
                if hasattr(state, 'optimization_start_time') and state.optimization_start_time:
                    optimization_duration = datetime.now() - state.optimization_start_time
                    duration_seconds = optimization_duration.total_seconds()
                    st.metric("Processing Time", f"{duration_seconds:.1f}s")

            # Count imports, fixtures, and test functions in both scripts
            original_imports = len(re.findall(r'^import |^from ', original_script, re.MULTILINE))
            original_fixtures = len(re.findall(r'@pytest\.fixture', original_script, re.MULTILINE))
            original_tests = len(re.findall(r'def test_', original_script, re.MULTILINE))

            optimized_imports = len(re.findall(r'^import |^from ', optimized_script_content, re.MULTILINE))
            optimized_fixtures = len(re.findall(r'@pytest\.fixture', optimized_script_content, re.MULTILINE))
            optimized_tests = len(re.findall(r'def test_', optimized_script_content, re.MULTILINE))

            # Display detailed metrics
            st.markdown("#### Code Structure Comparison")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Imports", f"{optimized_imports}",
                          delta=f"{optimized_imports - original_imports:+}")

            with col2:
                st.metric("Fixtures", f"{optimized_fixtures}",
                          delta=f"{optimized_fixtures - original_fixtures:+}")

            with col3:
                st.metric("Test Functions", f"{optimized_tests}",
                          delta=f"{optimized_tests - original_tests:+}")

            logger.info(f"Stage 8: Optimization metrics - Original: {original_lines} lines, {original_chars} chars; " +
                       f"Optimized: {optimized_lines} lines, {optimized_chars} chars; " +
                       f"Diff: {lines_diff} lines ({lines_percent:.1f}%), {chars_diff} chars ({chars_percent:.1f}%)")

            # Display the optimized script in an expander
            with st.expander("View Optimized Script", expanded=True):
                st.code(optimized_script_content, language="python")
                st.info(f"Script file: {state.optimized_script_path}")

            # Add a download button for the optimized script
            st.download_button(
                label="Download Optimized Script",
                data=optimized_script_content,
                file_name=os.path.basename(state.optimized_script_path),
                mime="text/plain",
                key="download_optimized_script"
            )

            # Option to view the original script for comparison
            with st.expander("View Original Script (Before Optimization)", expanded=False):
                st.code(original_script, language="python")

        except Exception as e:
            error_msg = f"Error displaying optimization results: {str(e)}"
            st.error(error_msg)
            logger.error(f"Stage 8: {error_msg}", exc_info=True)

        # Add a button to return to Phase 3 to select a new test case
        st.markdown("""
        <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
            <p style="font-size: 16px; color: #4CAF50; margin: 0;">Script optimization complete</p>
            <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">Click the button below to return to Phase 3 and select a new test case</p>
        </div>
        """, unsafe_allow_html=True)

        if st.button("Return to Test Case Selection (Phase 3)", use_container_width=True):
            logger.info("Phase 8: User clicked 'Return to Test Case Selection' button")

            # Reset test case state with confirmation
            state.reset_test_case_state(confirm=True, reason="Script optimization complete, returning to Phase 3")
            logger.info("Phase 8: Reset test case state to prepare for new test case")

            # Set a flag to indicate we're transitioning from Phase 8 to Phase 3
            st.session_state['transitioning_from_stage8'] = True
            st.session_state['stage_progression_message'] = "✅ Script optimization complete. Returning to Phase 3 to select a new test case."
            logger.info("Phase 8: Set transitioning_from_stage8 flag for workflow transition")

            # Force state update in session state
            st.session_state['state'] = state
            logger.info("Phase 8: Rerunning app to transition to Phase 3")
            st.rerun()

        return

    # If optimization is in progress, show a spinner
    if state.optimization_in_progress:
        with st.spinner("Optimizing script... This may take a minute or two."):
            try:
                # Get the combined script content
                combined_script = state.combined_script_content
                logger.info(f"Stage 8: Starting script optimization process for script with {len(combined_script)} characters")

                # Initialize optimization start time if not already set
                if not hasattr(state, 'optimization_start_time') or not state.optimization_start_time:
                    state.optimization_start_time = datetime.now()
                    logger.info(f"Stage 8: Setting optimization start time to {state.optimization_start_time}")

                # Check if the script is too large for a single optimization
                if len(combined_script) > 30000:  # 30k character limit for safety
                    st.info("Script is large, using chunked optimization approach...")
                    logger.info("Stage 8: Script exceeds 30k characters, using chunked optimization approach")

                    # Split the script into logical sections
                    # This is a simple approach - in a real implementation, you'd want more sophisticated parsing

                    # Extract imports section (all lines starting with import or from)
                    imports_pattern = r'((?:^import .*$|^from .*$)(?:\n|$))+'
                    imports_match = re.search(imports_pattern, combined_script, re.MULTILINE)
                    imports_section = imports_match.group(0) if imports_match else ""
                    logger.info(f"Stage 8: Extracted imports section ({len(imports_section)} characters)")

                    # Extract fixtures section (all @pytest.fixture blocks)
                    fixtures_pattern = r'(@pytest\.fixture.*?def .*?\):(?:\n    .*?)*?)(?=\n\n)'
                    fixtures_matches = re.finditer(fixtures_pattern, combined_script, re.DOTALL)
                    fixtures_section = "\n\n".join([m.group(0) for m in fixtures_matches]) if fixtures_matches else ""
                    logger.info(f"Stage 8: Extracted fixtures section ({len(fixtures_section)} characters)")

                    # Extract test functions (all def test_* blocks)
                    test_pattern = r'(def test_.*?\):(?:\n    .*?)*?)(?=\n\n|$)'
                    test_matches = re.finditer(test_pattern, combined_script, re.DOTALL)
                    test_functions_section = "\n\n".join([m.group(0) for m in test_matches]) if test_matches else ""
                    logger.info(f"Stage 8: Extracted test functions section ({len(test_functions_section)} characters)")

                    # Check if we successfully extracted all sections
                    if not imports_section and not fixtures_section and not test_functions_section:
                        logger.warning("Stage 8: Failed to extract any sections from the script, falling back to full script optimization")
                        st.warning("Could not split script into sections, attempting full optimization...")
                        # Fall back to optimizing the entire script
                        optimized_script = optimize_script_with_ai(
                            combined_script,
                            api_key=state.google_api_key
                        )
                    else:
                        # Optimize each section separately
                        logger.info("Stage 8: Optimizing imports section")
                        optimized_imports = optimize_script_with_ai(
                            imports_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 1, 'total_chunks': 3, 'chunk_type': 'imports'}
                        )
                        logger.info(f"Stage 8: Imports section optimization complete ({len(optimized_imports)} characters)")

                        logger.info("Stage 8: Optimizing fixtures section")
                        optimized_fixtures = optimize_script_with_ai(
                            fixtures_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 2, 'total_chunks': 3, 'chunk_type': 'fixtures'}
                        )
                        logger.info(f"Stage 8: Fixtures section optimization complete ({len(optimized_fixtures)} characters)")

                        logger.info("Stage 8: Optimizing test functions section")
                        optimized_tests = optimize_script_with_ai(
                            test_functions_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 3, 'total_chunks': 3, 'chunk_type': 'test_functions'}
                        )
                        logger.info(f"Stage 8: Test functions section optimization complete ({len(optimized_tests)} characters)")

                        # Combine the optimized sections
                        optimized_script = f"{optimized_imports}\n\n{optimized_fixtures}\n\n{optimized_tests}"
                        logger.info(f"Stage 8: Combined optimized sections into final script ({len(optimized_script)} characters)")
                else:
                    # Optimize the entire script at once
                    logger.info("Stage 8: Optimizing entire script in one call")
                    optimized_script = optimize_script_with_ai(
                        combined_script,
                        api_key=state.google_api_key
                    )
                    logger.info(f"Stage 8: Script optimization complete ({len(optimized_script)} characters)")

                # Save the optimized script to a file
                script_dir = "generated_tests"
                os.makedirs(script_dir, exist_ok=True)
                logger.info(f"Stage 8: Ensured output directory exists: {script_dir}")

                # Get the test case ID
                test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

                # Create a file path for the optimized script
                timestamp = int(time.time())
                optimized_script_file = os.path.join(
                    script_dir,
                    f"test_{test_case_id}_optimized_{timestamp}.py"
                )
                logger.info(f"Stage 8: Created output file path: {optimized_script_file}")

                # Save the optimized script to a file
                try:
                    with open(optimized_script_file, "w") as f:
                        f.write(optimized_script)
                    logger.info(f"Stage 8: Successfully saved optimized script to {optimized_script_file}")
                except Exception as e:
                    error_msg = f"Error saving optimized script: {str(e)}"
                    logger.error(f"Stage 8: {error_msg}")
                    st.error(error_msg)
                    state.optimization_in_progress = False
                    st.session_state['state'] = state
                    st.rerun()
                    return

                # Calculate optimization duration
                if hasattr(state, 'optimization_start_time') and state.optimization_start_time:
                    optimization_duration = datetime.now() - state.optimization_start_time
                    logger.info(f"Stage 8: Optimization completed in {optimization_duration.total_seconds():.2f} seconds")

                # Update the state
                state.optimized_script_path = optimized_script_file
                state.optimized_script_content = optimized_script
                state.optimization_complete = True
                state.optimization_in_progress = False
                logger.info("Stage 8: Updated state with optimization results and set optimization_complete=True")

                # Force state update in session state
                st.session_state['state'] = state
                logger.info("Stage 8: Rerunning app to display optimization results")
                st.rerun()

            except Exception as e:
                error_msg = f"Error optimizing script: {str(e)}"
                st.error(error_msg)
                logger.error(f"Stage 8: {error_msg}", exc_info=True)
                import traceback
                st.error(traceback.format_exc())
                logger.error(f"Stage 8: Detailed error traceback: {traceback.format_exc()}")

                # Reset the optimization state
                state.optimization_in_progress = False
                logger.info("Stage 8: Reset optimization_in_progress to False after error")

                # Force state update in session state
                st.session_state['state'] = state
                logger.info("Stage 8: Rerunning app after error")
                st.rerun()

    # If optimization is not in progress and not complete, show the optimization options
    st.markdown("#### Optimization Options")

    # Display information about the optimization process
    with st.expander("About Script Optimization", expanded=False):
        st.info("""
        This stage will optimize the combined script using Google AI to create a well-structured,
        cohesive PyTest module following best practices. The optimization process will:

        1. Refactor the script into a well-structured PyTest module
        2. Ensure proper test setup and teardown with appropriate fixtures
        3. Remove redundancies and optimize the code
        4. Maintain all functionality from the original steps
        5. Follow best practices for PyTest automation
        6. Add clear comments explaining the optimization process
        7. Preserve all imports, assertions, and test logic
        """)

        # Add more detailed information about what happens during optimization
        st.markdown("""
        #### What happens during optimization?

        The optimization process analyzes your combined script and makes improvements in several areas:

        - **Code Structure**: Organizes imports, fixtures, and test functions in a logical way
        - **Redundancy Removal**: Eliminates duplicate code and consolidates similar operations
        - **Error Handling**: Improves exception handling and adds appropriate try/except blocks
        - **Documentation**: Adds clear comments and docstrings to explain the test flow
        - **Best Practices**: Applies PyTest best practices for maintainable test automation

        The original functionality is preserved while making the code more efficient and easier to maintain.
        """)

    # Display script statistics
    if hasattr(state, 'combined_script_content') and state.combined_script_content:
        combined_script = state.combined_script_content
        line_count = combined_script.count('\n') + 1
        char_count = len(combined_script)

        # Count imports, fixtures, and test functions
        import_count = len(re.findall(r'^import |^from ', combined_script, re.MULTILINE))
        fixture_count = len(re.findall(r'@pytest\.fixture', combined_script, re.MULTILINE))
        test_count = len(re.findall(r'def test_', combined_script, re.MULTILINE))

        st.markdown("#### Script Statistics")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Lines", f"{line_count:,}")
        with col2:
            st.metric("Imports", f"{import_count}")
        with col3:
            st.metric("Fixtures", f"{fixture_count}")
        with col4:
            st.metric("Test Functions", f"{test_count}")

        logger.info(f"Stage 8: Script statistics - Lines: {line_count}, Chars: {char_count}, Imports: {import_count}, Fixtures: {fixture_count}, Tests: {test_count}")

    # Add a button to start the optimization with clear visual emphasis
    st.markdown("""
    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
        <p style="font-size: 16px; color: #4CAF50; margin: 0;">Ready to optimize your test script?</p>
        <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">Click the button below to start the optimization process</p>
    </div>
    """, unsafe_allow_html=True)

    if st.button("Start Script Optimization", use_container_width=True, key="start_optimization_button"):
        logger.info("Phase 8: User clicked 'Start Script Optimization' button")

        # Initialize optimization start time
        state.optimization_start_time = datetime.now()
        logger.info(f"Phase 8: Setting optimization start time to {state.optimization_start_time}")

        # Set the optimization in progress flag
        state.optimization_in_progress = True
        logger.info("Phase 8: Set optimization_in_progress flag to True")

        # Force state update in session state
        st.session_state['state'] = state
        logger.info("Phase 8: Rerunning app to start optimization process")
        st.rerun()
